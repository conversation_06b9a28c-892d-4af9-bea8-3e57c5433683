import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/providers/category_store_provider.dart';

final class CategoryIdsNotifier extends IdListNotifier {}

final categoryIdsProvider =
    NotifierProvider.autoDispose<CategoryIdsNotifier, List<int>>(
      CategoryIdsNotifier.new,
    );

final categoriesProvider = Provider.autoDispose<List<CategoryData>>((ref) {
  final ids = ref.watch(categoryIdsProvider);
  final store = ref.watch(categoryStoreProvider);
  final items = <CategoryData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing categories's reactivity.
/// This is the recommended way to manage categories.
final class CategoriesReactiveService {
  const CategoriesReactiveService(this.ref);

  final Ref ref;

  /// Get categories list
  List<CategoryData> getAll() {
    return ref.read(categoriesProvider);
  }

  /// Get category IDs
  List<int> getAllIds() {
    return ref.read(categoryIdsProvider);
  }

  /// Add a new category (adds to both global store and list)
  void addItem(CategoryData category) {
    // Add to global category store
    ref.read(categoryStoreProvider.notifier).addItem(category);

    // Add to categories list
    ref.read(categoryIdsProvider.notifier).addItem(category.id);
  }

  /// Add multiple categories
  void addItems(List<CategoryData> categories) {
    if (categories.isEmpty) return;

    // Add to global category store
    ref.read(categoryStoreProvider.notifier).addItems(categories);

    // Add to categories list
    final categoryIds = categories.map((category) => category.id).toList();
    ref.read(categoryIdsProvider.notifier).addItems(categoryIds);
  }

  /// Remove a category
  void remove(int categoryId) {
    // Remove from categories list
    ref.read(categoryIdsProvider.notifier).removeItem(categoryId);
  }

  /// Remove multiple categories
  void removeItems(List<int> categoryIds) {
    if (categoryIds.isEmpty) return;

    // Remove from categories list
    ref.read(categoryIdsProvider.notifier).removeItems(categoryIds);
  }

  /// Update a category in the store (automatically reflects in list)
  void updateItem(CategoryData updatedCategory) {
    ref.read(categoryStoreProvider.notifier).updateItem(updatedCategory);
  }

  /// Update multiple categories in the store
  void updateItems(List<CategoryData> updatedCategories) {
    ref.read(categoryStoreProvider.notifier).updateItems(updatedCategories);
  }

  /// Replace all categories
  void replaceAll(List<CategoryData> categories) {
    // Add to global category store
    ref.read(categoryStoreProvider.notifier).addItems(categories);

    // Replace categories list
    final categoryIds = categories.map((category) => category.id).toList();
    ref.read(categoryIdsProvider.notifier).replaceAll(categoryIds);
  }

  /// Clear all categories
  void clear() {
    // Clear the categories list
    ref.read(categoryIdsProvider.notifier).clear();
  }

  /// Get categories count
  int get count => ref.read(categoryIdsProvider.notifier).length;

  /// Check if list is empty
  bool get isEmpty => ref.read(categoryIdsProvider.notifier).isEmpty;

  /// Check if list is not empty
  bool get isNotEmpty => ref.read(categoryIdsProvider.notifier).isNotEmpty;
}

/// Provider for the CategoriesReactiveService.
final categoriesReactiveServiceProvider =
    Provider.autoDispose<CategoriesReactiveService>((ref) {
      return CategoriesReactiveService(ref);
    });

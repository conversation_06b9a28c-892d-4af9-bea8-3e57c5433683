// ids_map_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';

abstract class IdsMapNotifier extends Notifier<Map<String, List<int>>> {
  @override
  Map<String, List<int>> build() => {};

  void _updateState(Map<String, List<int>> newState) {
    state = newState;
  }

  bool hasItem(String slug) => state.containsKey(slug);

  List<int>? getIds(String slug) => state[slug];

  List<String> get slugs => state.keys.toList();

  List<int> get allIds => state.values.expand((ids) => ids).toList();

  void addItem(String slug, int id) {
    final updated = Map<String, List<int>>.from(state);
    if (updated.containsKey(slug)) {
      if (!updated[slug]!.contains(id)) {
        updated[slug] = [...updated[slug]!, id];
      }
    } else {
      updated[slug] = [id];
    }
    _updateState(updated);
  }

  void addItems(String slug, List<int> ids) {
    if (ids.isEmpty) return;
    final updated = Map<String, List<int>>.from(state);
    if (updated.containsKey(slug)) {
      final existingIds = updated[slug]!.toSet();
      final newIds = ids.where((id) => !existingIds.contains(id)).toList();
      if (newIds.isNotEmpty) {
        updated[slug] = [...updated[slug]!, ...newIds];
      }
    } else {
      updated[slug] = [...ids];
    }
    _updateState(updated);
  }

  void addItemsMap(Map<String, List<int>> newItems) {
    if (newItems.isEmpty) return;
    final updated = Map<String, List<int>>.from(state);
    for (final entry in newItems.entries) {
      final slug = entry.key;
      final ids = entry.value;
      if (updated.containsKey(slug)) {
        final existingIds = updated[slug]!.toSet();
        final newIds = ids.where((id) => !existingIds.contains(id)).toList();
        if (newIds.isNotEmpty) {
          updated[slug] = [...updated[slug]!, ...newIds];
        }
      } else {
        updated[slug] = [...ids];
      }
    }
    _updateState(updated);
  }

  void updateItems(String slug, List<int> ids) {
    if (!state.containsKey(slug)) return;
    final updated = Map<String, List<int>>.from(state);
    updated[slug] = [...ids];
    _updateState(updated);
  }

  void upsertItems(String slug, List<int> ids) {
    final updated = Map<String, List<int>>.from(state);
    updated[slug] = [...ids];
    _updateState(updated);
  }

  void removeItem(String slug) {
    if (!state.containsKey(slug)) return;
    final updated = Map<String, List<int>>.from(state);
    updated.remove(slug);
    _updateState(updated);
  }

  void removeItems(List<String> slugs) {
    if (slugs.isEmpty) return;
    final updated = Map<String, List<int>>.from(state);
    for (final slug in slugs) {
      updated.remove(slug);
    }
    _updateState(updated);
  }

  void removeIdFromSlug(String slug, int id) {
    if (!state.containsKey(slug)) return;
    final updated = Map<String, List<int>>.from(state);
    updated[slug] = updated[slug]!
        .where((existingId) => existingId != id)
        .toList();
    if (updated[slug]!.isEmpty) {
      updated.remove(slug);
    }
    _updateState(updated);
  }

  void replaceAll(Map<String, List<int>> newMap) {
    _updateState(newMap);
  }

  void clear() {
    _updateState({});
  }

  // Getter methods for better performance.

  int get length => state.length;

  bool get isEmpty => state.isEmpty;

  bool get isNotEmpty => state.isNotEmpty;

  int getIdsCount(String slug) => state[slug]?.length ?? 0;

  int get totalIdsCount => state.values.fold(0, (sum, ids) => sum + ids.length);
}

import 'dart:convert';

import 'package:flutter/foundation.dart';

@immutable
class CategoryData {
  const CategoryData({
    this.id = 0,
    this.slug = '',
    this.name = '',
    this.url = '',
    this.latestPhotoUrl = '',
    this.photosCount = 0,
  });

  final int id;
  final String slug;
  final String name;
  final String url;
  final String latestPhotoUrl;
  final int photosCount;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'slug': slug,
      'name': name,
      'url': url,
      'latestPhotoUrl': latestPhotoUrl,
      'photosCount': photosCount,
    };
  }

  factory CategoryData.fromMap(Map<String, dynamic> data) {
    return CategoryData(
      id: data['id'],
      slug: data['slug'],
      name: data['name'],
      url: data['url'],
      latestPhotoUrl: data['latestPhotoUrl'],
      photosCount: data['photosCount'],
    );
  }

  factory CategoryData.fromJson(String source) =>
      CategoryData.fromMap(json.decode(source));

  CategoryData copyWith({
    int? id,
    String? slug,
    String? name,
    String? url,
    String? latestPhotoUrl,
    int? photosCount,
  }) {
    return CategoryData(
      id: id ?? this.id,
      slug: slug ?? this.slug,
      name: name ?? this.name,
      url: url ?? this.url,
      latestPhotoUrl: latestPhotoUrl ?? this.latestPhotoUrl,
      photosCount: photosCount ?? this.photosCount,
    );
  }

  @override
  int get hashCode =>
      Object.hash(id, slug, name, url, latestPhotoUrl, photosCount);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CategoryData) return false;

    return other.id == id &&
        other.slug == slug &&
        other.name == name &&
        other.url == url &&
        other.latestPhotoUrl == latestPhotoUrl &&
        other.photosCount == photosCount;
  }
}

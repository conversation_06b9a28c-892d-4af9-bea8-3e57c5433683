import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';

final class SearchArtistIdsNotifier extends IdListNotifier {}

final searchArtistIdsProvider =
    NotifierProvider.autoDispose<SearchArtistIdsNotifier, List<int>>(
      SearchArtistIdsNotifier.new,
    );

final searchArtistsProvider = Provider.autoDispose<List<ArtistData>>((ref) {
  final ids = ref.watch(searchArtistIdsProvider);
  final store = ref.watch(artistStoreProvider);
  final items = <ArtistData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing search artists's reactivity.
/// This is the recommended way to manage search artists.
final class SearchArtistsReactiveService {
  const SearchArtistsReactiveService(this.ref);

  final Ref ref;

  /// Get search artists list
  List<ArtistData> getAll() {
    return ref.read(searchArtistsProvider);
  }

  /// Get search artist IDs
  List<int> getAllIds() {
    return ref.read(searchArtistIdsProvider);
  }

  /// Add a new search artist (adds to both global store and search list)
  void addItem(ArtistData artist) {
    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItem(artist);

    // Add to search artists list
    ref.read(searchArtistIdsProvider.notifier).addItem(artist.id);
  }

  /// Add multiple search artists
  void addItems(List<ArtistData> artists) {
    if (artists.isEmpty) return;

    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItems(artists);

    // Add to search artists list
    final artistIds = artists.map((artist) => artist.id).toList();
    ref.read(searchArtistIdsProvider.notifier).addItems(artistIds);
  }

  /// Remove a search artist
  void remove(int artistId) {
    // Remove from search artists list
    ref.read(searchArtistIdsProvider.notifier).removeItem(artistId);
  }

  /// Remove multiple search artists
  void removeItems(List<int> artistIds) {
    if (artistIds.isEmpty) return;

    // Remove from search artists list
    ref.read(searchArtistIdsProvider.notifier).removeItems(artistIds);
  }

  /// Update an artist in the store (automatically reflects in search list)
  void updateItem(ArtistData updatedArtist) {
    ref.read(artistStoreProvider.notifier).updateItem(updatedArtist);
  }

  /// Update multiple artists in the store
  void updateItems(List<ArtistData> updatedArtists) {
    ref.read(artistStoreProvider.notifier).updateItems(updatedArtists);
  }

  /// Replace all search artists
  void replaceAll(List<ArtistData> artists) {
    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItems(artists);

    // Replace search artists list
    final artistIds = artists.map((artist) => artist.id).toList();
    ref.read(searchArtistIdsProvider.notifier).replaceAll(artistIds);
  }

  /// Clear all search artists
  void clear() {
    // Clear the search artists list
    ref.read(searchArtistIdsProvider.notifier).clear();
  }

  /// Get search artists count
  int get searchCount => ref.read(searchArtistIdsProvider.notifier).length;

  /// Check if search list is empty
  bool get isEmpty => ref.read(searchArtistIdsProvider.notifier).isEmpty;

  /// Check if search list is not empty
  bool get isNotEmpty => ref.read(searchArtistIdsProvider.notifier).isNotEmpty;
}

/// Provider for the SearchArtistsReactiveService.
final searchArtistsReactiveServiceProvider =
    Provider.autoDispose<SearchArtistsReactiveService>((ref) {
      return SearchArtistsReactiveService(ref);
    });

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class CameraPhotoIdsNotifier extends IdListNotifier {}

final cameraPhotoIdsProvider =
    NotifierProvider.autoDispose<CameraPhotoIdsNotifier, List<int>>(
      CameraPhotoIdsNotifier.new,
    );

final cameraPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(cameraPhotoIdsProvider);
  final store = ref.watch(photoStoreProvider);
  final items = <PhotoData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing camera photos's reactivity.
/// This is the recommended way to manage camera photos.
final class CameraPhotosReactiveService {
  const CameraPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get camera photos list
  List<PhotoData> getAll() {
    return ref.read(cameraPhotosProvider);
  }

  /// Get camera photo IDs
  List<int> getAllIds() {
    return ref.read(cameraPhotoIdsProvider);
  }

  /// Add a new camera photo (adds to both global store and camera photos list)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to camera photos list
    ref.read(cameraPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple camera photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to camera photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(cameraPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a camera photo
  void remove(int photoId) {
    // Remove from camera photos list
    ref.read(cameraPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple camera photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from camera photos list
    ref.read(cameraPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in camera photos list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all camera photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace camera photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(cameraPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  /// Clear all camera photos
  void clear() {
    // Clear the camera photos list
    ref.read(cameraPhotoIdsProvider.notifier).clear();
  }

  /// Get camera photos count
  int get count => ref.read(cameraPhotoIdsProvider.notifier).length;

  /// Check if camera photos list is empty
  bool get isEmpty => ref.read(cameraPhotoIdsProvider.notifier).isEmpty;

  /// Check if camera photos list is not empty
  bool get isNotEmpty => ref.read(cameraPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the CameraPhotosReactiveService.
final cameraPhotosReactiveServiceProvider =
    Provider.autoDispose<CameraPhotosReactiveService>((ref) {
      return CameraPhotosReactiveService(ref);
    });

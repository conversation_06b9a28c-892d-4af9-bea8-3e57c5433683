// id_list_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';

abstract class IdListNotifier extends Notifier<List<int>> {
  final Set<int> _idCache = {};

  @override
  List<int> build() => [];

  void _updateState(List<int> newState) {
    state = newState;
    _rebuildIdCache();
  }

  void _rebuildIdCache() {
    _idCache
      ..clear()
      ..addAll(state);
  }

  bool hasItem(int id) => _idCache.contains(id);

  int getIndex(int id) => state.indexOf(id);

  void addItem(int newItem, {bool reorder = false}) {
    if (_idCache.contains(newItem)) return;
    final updated = [...state, newItem];

    if (reorder) {
      updated.sort((a, b) => b.compareTo(a));
    }

    _updateState(updated);
  }

  void addItems(List<int> newItems, {bool reorder = false}) {
    final filtered = newItems.where((id) => !_idCache.contains(id));
    if (filtered.isEmpty) return;
    final updated = [...state, ...filtered];

    if (reorder) {
      updated.sort((a, b) => b.compareTo(a));
    }

    _updateState(updated);
  }

  void removeItem(int id) {
    if (!_idCache.contains(id)) return;
    final updated = state.where((item) => item != id).toList();
    _updateState(updated);
  }

  void removeItems(List<int> ids) {
    if (ids.isEmpty) return;
    final updated = state.where((item) => !ids.contains(item)).toList();
    _updateState(updated);
  }

  void replaceAll(List<int> newList) {
    _updateState(newList);
  }

  void clear() {
    _updateState([]);
  }

  // Getter methods for better performance.

  int get length => state.length;

  bool get isEmpty => state.isEmpty;

  bool get isNotEmpty => state.isNotEmpty;

  /// Get cached IDs count (should always match state.length)
  int get cachedCount => _idCache.length;

  /// Check if the cache is in sync (for debugging)
  bool get isCacheInSync => _idCache.length == state.length;
}

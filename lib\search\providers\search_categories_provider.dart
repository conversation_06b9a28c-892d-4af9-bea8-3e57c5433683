import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/providers/category_store_provider.dart';

final class SearchCategoryIdsNotifier extends IdListNotifier {}

final searchCategoryIdsProvider =
    NotifierProvider.autoDispose<SearchCategoryIdsNotifier, List<int>>(
      SearchCategoryIdsNotifier.new,
    );

final searchCategoriesProvider = Provider.autoDispose<List<CategoryData>>((
  ref,
) {
  final ids = ref.watch(searchCategoryIdsProvider);
  final store = ref.watch(categoryStoreProvider);
  final items = <CategoryData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing search categories's reactivity.
/// This is the recommended way to manage search categories.
final class SearchCategoriesReactiveService {
  const SearchCategoriesReactiveService(this.ref);

  final Ref ref;

  /// Get search categories list
  List<CategoryData> getAll() {
    return ref.read(searchCategoriesProvider);
  }

  /// Get search category IDs
  List<int> getAllIds() {
    return ref.read(searchCategoryIdsProvider);
  }

  /// Add a new search category (adds to both global store and search list)
  void addItem(CategoryData category) {
    // Add to global category store
    ref.read(categoryStoreProvider.notifier).addItem(category);

    // Add to search categories list
    ref.read(searchCategoryIdsProvider.notifier).addItem(category.id);
  }

  /// Add multiple search categories
  void addItems(List<CategoryData> categories) {
    if (categories.isEmpty) return;

    // Add to global category store
    ref.read(categoryStoreProvider.notifier).addItems(categories);

    // Add to search categories list
    final categoryIds = categories.map((category) => category.id).toList();
    ref.read(searchCategoryIdsProvider.notifier).addItems(categoryIds);
  }

  /// Remove a search category
  void remove(int categoryId) {
    // Remove from search categories list
    ref.read(searchCategoryIdsProvider.notifier).removeItem(categoryId);
  }

  /// Remove multiple search categories
  void removeItems(List<int> categoryIds) {
    if (categoryIds.isEmpty) return;

    // Remove from search categories list
    ref.read(searchCategoryIdsProvider.notifier).removeItems(categoryIds);
  }

  /// Update a category in the store (automatically reflects in search list)
  void updateItem(CategoryData updatedCategory) {
    ref.read(categoryStoreProvider.notifier).updateItem(updatedCategory);
  }

  /// Update multiple categories in the store
  void updateItems(List<CategoryData> updatedCategories) {
    ref.read(categoryStoreProvider.notifier).updateItems(updatedCategories);
  }

  /// Replace all search categories
  void replaceAll(List<CategoryData> categories) {
    // Add to global category store
    ref.read(categoryStoreProvider.notifier).addItems(categories);

    // Replace search categories list
    final categoryIds = categories.map((category) => category.id).toList();
    ref.read(searchCategoryIdsProvider.notifier).replaceAll(categoryIds);
  }

  /// Clear all search categories
  void clear() {
    // Clear the search categories list
    ref.read(searchCategoryIdsProvider.notifier).clear();
  }

  /// Get search categories count
  int get searchCount => ref.read(searchCategoryIdsProvider.notifier).length;

  /// Check if search list is empty
  bool get isEmpty => ref.read(searchCategoryIdsProvider.notifier).isEmpty;

  /// Check if search list is not empty
  bool get isNotEmpty =>
      ref.read(searchCategoryIdsProvider.notifier).isNotEmpty;
}

/// Provider for the SearchCategoriesReactiveService.
final searchCategoriesReactiveServiceProvider =
    Provider.autoDispose<SearchCategoriesReactiveService>((ref) {
      return SearchCategoriesReactiveService(ref);
    });

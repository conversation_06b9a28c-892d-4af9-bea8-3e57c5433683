import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/dto/category_photo_list_interaction_data.dart';
import 'package:portraitmode/category/providers/category_photo_list_interaction_provider.dart';
import 'package:portraitmode/category/providers/category_photos_provider.dart';
import 'package:portraitmode/category/widgets/category_detail_screen_list_mode.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/load_more/enum.dart';
import 'package:portraitmode/load_more/masonry_load_more.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';

class CategoryDetailScreenMasonryMode extends ConsumerStatefulWidget {
  const CategoryDetailScreenMasonryMode({super.key, required this.category});

  final CategoryData category;

  @override
  CategoryDetailScreenMasonryModeState createState() =>
      CategoryDetailScreenMasonryModeState();
}

class CategoryDetailScreenMasonryModeState
    extends ConsumerState<CategoryDetailScreenMasonryMode> {
  final _scrollController = ScrollController();
  final photoListService = PhotoListService();

  late final int _profileId;
  late final int _loadMorePerPage;
  bool _loadMoreEndReached = false;

  late final NotifierProvider<
    CategoryPhotoListInteractionNotifier,
    CategoryPhotoListInteractionData
  >
  _interactionProvider;

  @override
  void initState() {
    _interactionProvider = getCategoryPhotoListInteractionProvider(
      widget.category.slug,
    );
    _loadMorePerPage = LoadMoreConfig.camerasPerPage;
    _profileId = LocalUserService.userId ?? 0;
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // log('build screen: CategoryDetailScreenMasonryMode');
    final photoList = ref.watch(categoryPhotosProvider);

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 768.0),
            child: RefreshIndicator(
              onRefresh: _handleRefresh,
              child: NestedScrollView(
                headerSliverBuilder:
                    (BuildContext context, bool innerBoxIsScrolled) {
                      return [
                        SliverOverlapAbsorber(
                          handle:
                              NestedScrollView.sliverOverlapAbsorberHandleFor(
                                context,
                              ),
                        ),
                        // This PmSliverAppBar was originally inside of the
                        // SliverOverlapAbsorber under it's sliver property.
                        // I extracted it out because it was causing weird top gap.
                        PmSliverAppBar(
                          scrollController: _scrollController,
                          titleText: widget.category.name,
                          useLogo: false,
                          automaticallyImplyLeading: true,
                        ),
                      ];
                    },
                body: MasonryLoadMore(
                  scrollController: _scrollController,
                  isFinished: _loadMoreEndReached,
                  onLoadMore: _handleLoadMore,
                  loadingWidgetColor: context.colors.baseColorAlt,
                  runOnEmptyResult: true,
                  loadingStatusText: "",
                  finishedStatusText: "",
                  padding: const EdgeInsets.symmetric(
                    vertical: 8.0,
                    horizontal: 8.0,
                  ),
                  crossAxisCount: 2,
                  mainAxisSpacing: 8.0,
                  crossAxisSpacing: 8.0,
                  itemsCount: photoList.length,
                  itemBuilder: (BuildContext masonryContext, int index) {
                    if (index >= photoList.length) {
                      return const SizedBox.shrink();
                    }

                    return PhotoMasonryItem(
                      key: ValueKey(photoList[index].id),
                      index: index,
                      photo: photoList[index],
                      isOwnProfile: photoList[index].authorId == _profileId,
                      screenName: 'category_detail_screen',
                      onPhotoTap: () => _handleOnPhotoTap(index),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleOnPhotoTap(int itemIndex) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CategoryDetailScreenListMode(
          category: widget.category,
          initialScrollIndex: itemIndex,
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    ref.read(_interactionProvider.notifier).setLoadMoreLastId(0);
    _loadMoreEndReached = false;

    final PhotoListResponse response = await photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: 0,
      categoryId: widget.category.id,
    );

    _handlePhotosResponse(response, true, false);
  }

  Future<LoadMoreResult> _handleLoadMore() async {
    final loadMoreLastId = ref.read(_interactionProvider).loadMoreLastId;
    final isFirstLoad = loadMoreLastId == 0;

    final PhotoListResponse response = await photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: loadMoreLastId,
      categoryId: widget.category.id,
    );

    _handlePhotosResponse(response, false, isFirstLoad);

    if (!response.success) {
      return LoadMoreResult.failed;
    }

    if (response.data.isEmpty || response.data.length < _loadMorePerPage) {
      return LoadMoreResult.finished;
    }

    return LoadMoreResult.success;
  }

  void _handlePhotosResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final categoryPhotosReactiveService = ref.read(
      categoryPhotosReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      if (isRefresh) {
        categoryPhotosReactiveService.clear();
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    ref
        .read(_interactionProvider.notifier)
        .setLoadMoreLastId(response.data.last.id);

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      categoryPhotosReactiveService.replaceAll(response.data);
    } else {
      if (isFirstLoad) {
        categoryPhotosReactiveService.replaceAll(response.data);
      } else {
        categoryPhotosReactiveService.addItems(response.data);
      }
    }
  }
}

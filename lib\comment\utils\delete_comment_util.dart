import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/enum.dart';
import 'package:portraitmode/comment/http_responses/comment_response.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/comment/services/comment_service.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';

class DeleteCommentUtil {
  final BuildContext context;
  final WidgetRef ref;
  final int photoId;
  final CommentData commentToDelete;
  final GlobalKey targetKey;

  DeleteCommentUtil({
    required this.context,
    required this.ref,
    required this.photoId,
    required this.commentToDelete,
    required this.targetKey,
  });

  Future<void> handleDeleteEvent() async {
    final targetContext = targetKey.currentContext;

    if (targetContext != null) {
      await Scrollable.ensureVisible(
        targetContext,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        alignment: 0.0,
      );
    }

    ref
        .read(commentStoreProvider.notifier)
        .setSubmissionStatus(
          commentToDelete.id,
          CommentSubmissionStatus.deleting,
        );

    final commentService = CommentService();

    CommentResponse? response = await commentService.delete(commentToDelete.id);

    if (!response.success) {
      if (context.mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      // If the submission fails, we won't restore the text back into the input field.
      // We also won't remove the comment from the list.
      //
      // Instead, we may display a failure indicator to let the user retry,
      // or leave the comment visible with a "submitting" status.
      //
      // todo: handle "retry" condition.

      return;
    }

    // Bring back the submissionStatus before actually deleting it.
    //
    // This is important because parent comment totalReplies
    // won't be updated if the submissionStatus is other than submitted.
    ref
        .read(commentStoreProvider.notifier)
        .setSubmissionStatus(
          commentToDelete.id,
          CommentSubmissionStatus.submitted,
        );

    final photoCommentService = ref.read(photoCommentReactiveServiceProvider);

    photoCommentService.removeComment(photoId, commentToDelete.id);
  }
}

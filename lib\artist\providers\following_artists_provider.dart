import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';

final class FollowingArtistIdsNotifier extends IdListNotifier {}

final followingArtistIdsProvider =
    NotifierProvider.autoDispose<FollowingArtistIdsNotifier, List<int>>(
      FollowingArtistIdsNotifier.new,
    );

final followingArtistsProvider = Provider.autoDispose<List<ArtistData>>((ref) {
  final ids = ref.watch(followingArtistIdsProvider);
  final store = ref.watch(artistStoreProvider);
  final items = <ArtistData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing following artists's reactivity.
/// This is the recommended way to manage following artists.
final class FollowingArtistsReactiveService {
  const FollowingArtistsReactiveService(this.ref);

  final Ref ref;

  /// Get following artists list
  List<ArtistData> getAll() {
    return ref.read(followingArtistsProvider);
  }

  /// Get following artist IDs
  List<int> getAllIds() {
    return ref.read(followingArtistIdsProvider);
  }

  /// Add a new following artist (adds to both global store and following list)
  void addItem(ArtistData artist, {bool updateFollowingStatus = false}) {
    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItem(artist);

    // Add to following artists list
    ref.read(followingArtistIdsProvider.notifier).addItem(artist.id);

    // Update the artist's isFollowing status in the store
    if (updateFollowingStatus) {
      final updatedArtist = artist.copyWith(isFollowing: true);
      ref.read(artistStoreProvider.notifier).updateItem(updatedArtist);
    }
  }

  /// Add multiple following artists
  void addItems(
    List<ArtistData> artists, {
    bool updateFollowingStatus = false,
  }) {
    if (artists.isEmpty) return;

    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItems(artists);

    // Add to following artists list
    final artistIds = artists.map((artist) => artist.id).toList();
    ref.read(followingArtistIdsProvider.notifier).addItems(artistIds);

    // Update all artists' isFollowing status in the store
    if (updateFollowingStatus) {
      final updatedArtists = artists
          .map((artist) => artist.copyWith(isFollowing: true))
          .toList();

      ref.read(artistStoreProvider.notifier).updateItems(updatedArtists);
    }
  }

  /// Remove a following artist
  void remove(int artistId, {bool updateFollowingStatus = false}) {
    // Remove from following artists list
    ref.read(followingArtistIdsProvider.notifier).removeItem(artistId);

    // Update the artist's isFollowing status in the store if it exists
    if (updateFollowingStatus) {
      final artist = ref.read(artistStoreProvider.notifier).getItem(artistId);

      if (artist != null) {
        final updatedArtist = artist.copyWith(isFollowing: false);
        ref.read(artistStoreProvider.notifier).updateItem(updatedArtist);
      }
    }
  }

  /// Remove multiple following artists
  void removeItems(List<int> artistIds, {bool updateFollowingStatus = false}) {
    if (artistIds.isEmpty) return;

    // Remove from following artists list
    ref.read(followingArtistIdsProvider.notifier).removeItems(artistIds);

    // Update all artists' isFollowing status in the store
    if (updateFollowingStatus) {
      final artistStore = ref.read(artistStoreProvider.notifier);
      final updatedArtists = <ArtistData>[];

      for (final artistId in artistIds) {
        final artist = artistStore.getItem(artistId);
        if (artist != null) {
          updatedArtists.add(artist.copyWith(isFollowing: false));
        }
      }

      if (updatedArtists.isNotEmpty) {
        artistStore.updateItems(updatedArtists);
      }
    }
  }

  /// Check if an artist is being followed
  bool isFollowing(int artistId) {
    return ref.read(followingArtistIdsProvider.notifier).hasItem(artistId);
  }

  // Toggle following status for an artist
  void toggleFollowing(int artistId) {
    if (isFollowing(artistId)) {
      remove(artistId);
    } else {
      // Get artist from store and add to following if it exists
      final artist = ref.read(artistStoreProvider.notifier).getItem(artistId);
      if (artist != null) {
        addItem(artist);
      }
    }
  }

  // Update an artist in the store (automatically reflects in following list)
  void updateItem(ArtistData updatedArtist) {
    ref.read(artistStoreProvider.notifier).updateItem(updatedArtist);
  }

  // Update multiple artists in the store
  void updateItems(List<ArtistData> updatedArtists) {
    ref.read(artistStoreProvider.notifier).updateItems(updatedArtists);
  }

  // Replace all following artists
  void replaceAll(
    List<ArtistData> artists, {
    bool updateFollowingStatus = false,
  }) {
    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItems(artists);

    // Replace following artists list
    final artistIds = artists.map((artist) => artist.id).toList();
    ref.read(followingArtistIdsProvider.notifier).replaceAll(artistIds);

    // Update all artists' isFollowing status in the store
    if (updateFollowingStatus) {
      final updatedArtists = artists
          .map((artist) => artist.copyWith(isFollowing: true))
          .toList();

      ref.read(artistStoreProvider.notifier).updateItems(updatedArtists);
    }
  }

  // Clear all following artists
  void clear({bool updateFollowingStatus = false}) {
    final currentFollowingIds = getAllIds();

    // Clear the following artists list
    ref.read(followingArtistIdsProvider.notifier).clear();

    // Update all previously following artists' isFollowing status to false
    if (updateFollowingStatus) {
      if (currentFollowingIds.isNotEmpty) {
        final artistStore = ref.read(artistStoreProvider.notifier);
        final updatedArtists = <ArtistData>[];

        for (final artistId in currentFollowingIds) {
          final artist = artistStore.getItem(artistId);
          if (artist != null) {
            updatedArtists.add(artist.copyWith(isFollowing: false));
          }
        }

        if (updatedArtists.isNotEmpty) {
          artistStore.updateItems(updatedArtists);
        }
      }
    }
  }

  // Get following artists count
  int get followingCount =>
      ref.read(followingArtistIdsProvider.notifier).length;

  // Check if following list is empty
  bool get isEmpty => ref.read(followingArtistIdsProvider.notifier).isEmpty;

  // Check if following list is not empty
  bool get isNotEmpty =>
      ref.read(followingArtistIdsProvider.notifier).isNotEmpty;
}

/// Provider for the FollowingArtistsReactiveService.
final followingArtistsReactiveServiceProvider =
    Provider.autoDispose<FollowingArtistsReactiveService>((ref) {
      return FollowingArtistsReactiveService(ref);
    });

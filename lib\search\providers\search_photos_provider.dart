import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class SearchPhotoIdsNotifier extends IdListNotifier {}

final searchPhotoIdsProvider =
    NotifierProvider.autoDispose<SearchPhotoIdsNotifier, List<int>>(
      SearchPhotoIdsNotifier.new,
    );

final searchPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(searchPhotoIdsProvider);
  final store = ref.watch(photoStoreProvider);
  final items = <PhotoData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing search photos's reactivity.
/// This is the recommended way to manage search photos.
final class SearchPhotosReactiveService {
  const SearchPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get search photos list
  List<PhotoData> getAll() {
    return ref.read(searchPhotosProvider);
  }

  /// Get search photo IDs
  List<int> getAllIds() {
    return ref.read(searchPhotoIdsProvider);
  }

  /// Add a new search photo (adds to both global store and search list)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to search photos list
    ref.read(searchPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple search photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to search photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(searchPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a search photo
  void remove(int photoId) {
    // Remove from search photos list
    ref.read(searchPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple search photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from search photos list
    ref.read(searchPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in search list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all search photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace search photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(searchPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  /// Clear all search photos
  void clear() {
    // Clear the search photos list
    ref.read(searchPhotoIdsProvider.notifier).clear();
  }

  /// Get search photos count
  int get searchCount => ref.read(searchPhotoIdsProvider.notifier).length;

  /// Check if search list is empty
  bool get isEmpty => ref.read(searchPhotoIdsProvider.notifier).isEmpty;

  /// Check if search list is not empty
  bool get isNotEmpty => ref.read(searchPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the SearchPhotosReactiveService.
final searchPhotosReactiveServiceProvider =
    Provider.autoDispose<SearchPhotosReactiveService>((ref) {
      return SearchPhotosReactiveService(ref);
    });

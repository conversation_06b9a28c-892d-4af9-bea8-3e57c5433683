import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_place/google_place.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/widgets/categories_picker.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/feedback_token/providers/feedback_token_amount_provider.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/form/utils/field_validators.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/location_picker/location_picker.dart';
import 'package:portraitmode/modals/modal_drag_handle.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/dto/photo_upload_data.dart';
import 'package:portraitmode/photo/http_responses/upload_photo_response.dart';
import 'package:portraitmode/photo/providers/latest_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/upload_photo_service.dart';
import 'package:portraitmode/photo/widgets/photo_upload/photo_picker.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

class UploadPhotoModal extends ConsumerStatefulWidget {
  final ScrollController scrollController;

  const UploadPhotoModal({super.key, required this.scrollController});

  @override
  UploadPhotoModalState createState() => UploadPhotoModalState();
}

class UploadPhotoModalState extends ConsumerState<UploadPhotoModal> {
  final _formKey = GlobalKey<FormState>();
  final GooglePlace _googlePlace = GooglePlace(GoogleConfig.mapApiKey);

  final _descriptionFieldController = TextEditingController();
  final _locationFieldController = TextEditingController();

  double? _locationLat;
  double? _locationLng;
  String _locationAddress = '';

  List<CategoryData> _selectedCategories = [];
  File? _selectedPhoto;
  bool _needsFeedback = false;
  late Function? _cleanPreview;

  @override
  void initState() {
    super.initState();

    _descriptionFieldController.text = globalPhotoUploadData.description ?? '';
    _locationFieldController.text = globalPhotoUploadData.address;

    _locationLat = globalPhotoUploadData.lat;
    _locationLng = globalPhotoUploadData.lng;
    _locationAddress = globalPhotoUploadData.address;

    _selectedCategories = globalPhotoUploadData.categories;
    _needsFeedback = globalPhotoUploadData.needsFeedback;
    _selectedPhoto = globalPhotoUploadData.photo;
  }

  @override
  void dispose() {
    _descriptionFieldController.dispose();
    _locationFieldController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 8.0,
        bottom: MediaQuery.viewInsetsOf(context).bottom + 15.0,
      ),
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      child: SingleChildScrollView(
        controller: widget.scrollController,
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              const SizedBox(height: 10.0, child: ModalDragHandle()),
              Padding(
                padding: const EdgeInsets.only(
                  top: 8.0,
                  bottom: 16.0,
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                ),
                child: PhotoPicker(
                  onInit: (cleanPreview) {
                    _cleanPreview = cleanPreview;
                  },
                  onChange: (photo) {
                    if (!mounted) return;

                    setState(() {
                      _selectedPhoto = photo;
                      globalPhotoUploadData.photo = _selectedPhoto;
                    });
                  },
                  defaultPhoto: _selectedPhoto,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  top: 8.0,
                  bottom: 16.0,
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                ),
                child: PmTextField(
                  controller: _locationFieldController,
                  readOnly: true,
                  labelText: "Photo location",
                  validator: FieldValidators.locationValidator.call,
                  onTap: () async {
                    _openLocationPicker(context);
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  top: 8.0,
                  bottom: 16.0,
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                ),
                child: PmTextField(
                  controller: _descriptionFieldController,
                  textCapitalization: TextCapitalization.sentences,
                  onChanged: _handleDescriptionChanged,
                  labelText: "Caption",
                  // validator: FieldValidators.photoDescriptionValidator,
                ),
              ),
              Builder(
                builder: (context) {
                  if (globalFeedbackTokensAmount < 1) {
                    return const SizedBox.shrink();
                  }

                  return _buildRequestFeedbackField();
                },
              ),
              Padding(
                padding: const EdgeInsets.only(
                  top: 8.0,
                  bottom: 16.0,
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.0),
                    border: Border.all(
                      color: context.isDarkMode
                          ? AppColorsCache.dark().baseColorAlt
                          : AppColorsCache.light().baseColor,
                      width: 1.0,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => CategoriesPicker(
                                selectedCategories: _selectedCategories,
                                onClose: (cats) {
                                  FocusScope.of(context).unfocus();
                                  setState(() {
                                    _selectedCategories = cats;
                                    globalPhotoUploadData.categories =
                                        _selectedCategories;
                                  });
                                },
                              ),
                            ),
                          );
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Ionicons.add_circle,
                              color: context.colors.accentColor,
                              size: 20.0,
                            ),
                            const SizedBox(width: 4.0),
                            Text(
                              "Add categories",
                              style: TextStyle(
                                color: context.colors.accentColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (_selectedCategories.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 4.0,
                            right: 4.0,
                            bottom: 2.0,
                          ),
                          child: Wrap(
                            spacing: 8.0,
                            runSpacing: -6.0,
                            children: _selectedCategories
                                .map(
                                  (cat) => Chip(
                                    label: Text(cat.name),
                                    onDeleted: () {
                                      setState(() {
                                        _selectedCategories.remove(cat);
                                      });
                                    },
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  top: 8.0,
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                ),
                child: SafeArea(
                  child: SubmitButton(
                    buttonText: "Submit photo",
                    width: double.infinity,
                    height: 40.0,
                    fontWeight: FontWeight.w600,
                    onPressed: _handleSubmission,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRequestFeedbackField() {
    WidgetStateProperty<Color>? checkboxFillColor = !_needsFeedback
        ? WidgetStateProperty.all<Color>(context.colors.scaffoldColor)
        : WidgetStateProperty.all<Color>(context.colors.accentColor);

    final Color checkboxBorderColor = context.isDarkMode
        ? AppColorsCache.dark().darkerGreyColor
        : AppColorsCache.light().greyColor;

    return Padding(
      padding: const EdgeInsets.only(
        bottom: 8.0,
        left: 4.0,
        right: ScreenStyleConfig.horizontalPadding,
      ),
      child: InkWell(
        onTap: _handleRequestFeedbackTap,
        child: Row(
          children: [
            Checkbox(
              value: _needsFeedback,
              visualDensity: VisualDensity.compact,
              // Disable direct interaction with the checkbox
              onChanged: null,
              activeColor: context.colors.accentColor,
              checkColor: context.colors.baseColor,
              fillColor: checkboxFillColor,
              side: BorderSide(color: checkboxBorderColor, width: 1.2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4.0),
              ),
            ),
            Text(
              "Request feedback",
              style: TextStyle(
                fontSize: 15.0,
                color: context.colors.brandColorAlt,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleDescriptionChanged(String value) {
    globalPhotoUploadData.description = value;
  }

  void _openLocationPicker(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) {
          return LocationPicker(onChanged: _handleLocationChanged);
        },
      ),
    );
  }

  void _handleLocationChanged(AutocompletePrediction prediction) async {
    _locationFieldController.text = prediction.description ?? _locationAddress;

    if (mounted) {
      setState(() {
        _locationAddress = prediction.description ?? _locationAddress;
        globalPhotoUploadData.address = _locationAddress;
      });
    }

    DetailsResponse? response = await _getDetailedResult(prediction);
    if (response == null) return;

    DetailsResult? result = response.result;
    if (result == null) return;
    if (result.formattedAddress == null) return;

    _locationFieldController.text =
        result.formattedAddress ?? _locationFieldController.text;

    Geometry? geometry = result.geometry;
    if (geometry == null) return;

    Location? location = geometry.location;
    if (location == null) return;
    if (location.lat == null || location.lng == null) return;

    if (mounted) {
      setState(() {
        _locationAddress = result.formattedAddress ?? _locationAddress;
        _locationLat = location.lat;
        _locationLng = location.lng;

        globalPhotoUploadData.address = _locationAddress;
        globalPhotoUploadData.lat = _locationLat;
        globalPhotoUploadData.lng = _locationLng;
      });
    }
  }

  Future<DetailsResponse?> _getDetailedResult(
    AutocompletePrediction prediction,
  ) async {
    if (prediction.placeId == null) return null;

    DetailsResponse? result = await _googlePlace.details.get(
      prediction.placeId ?? '',
    );

    return result;
  }

  void _handleRequestFeedbackTap() {
    if (!mounted) return;
    final bool checked = !_needsFeedback;

    setState(() {
      _needsFeedback = checked;
      globalPhotoUploadData.needsFeedback = checked;
    });
  }

  final _uploadPhotoService = UploadPhotoService();

  Future<void> _handleSubmission() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedPhoto == null) return;

    Uint8List photoBytes = await _selectedPhoto!.readAsBytes();

    UploadPhotoResponse response = await _uploadPhotoService.submit(
      photoBytes: photoBytes,
      address: _locationAddress,
      lat: _locationLat.toString(),
      lng: _locationLng.toString(),
      description: _descriptionFieldController.text,
      categoryIds: _selectedCategories.map((cat) => cat.id).toList(),
      needsFeedback: _needsFeedback,
    );

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        if (mounted) {
          showSessionEndedDialog(context, ref);
        }

        return;
      }

      if (mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              content: Text(response.message),
              actions: <Widget>[
                TextButton(
                  child: const Text("Close"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      }

      return;
    }

    // Reset the global photoUploadData.
    globalPhotoUploadData = PhotoUploadData();

    PhotoData data = response.data ?? const PhotoData();

    ref.read(photoStoreProvider.notifier).addItem(data, updateIfExists: true);

    ref.read(latestPhotoIdsProvider.notifier).addItem(data.id, reorder: true);

    // After uploading a photo (and the status is success),
    // it will redirect to profile screen.
    // That means, the initState() of profile screen will be called.
    // That means, the load more related state will be reset.
    //
    // So, we need to reset "all-photos" album provider
    // instead of adding the newly uploaded photo to the provider.
    myAlbumPhotoListProviderMap.forEach((
      String albumSlug,
      NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>> provider,
    ) {
      if (albumSlug == 'all-photos') {
        ref.read(provider.notifier).clear();
      }
    });

    ref.read(myAlbumProvider.notifier).incrementTotalPhotos('all-photos');
    _cleanPreview?.call();

    _locationFieldController.text = '';
    _descriptionFieldController.text = '';

    if (mounted) {
      setState(() {
        _selectedCategories = [];
        _selectedPhoto = null;
      });
    }

    final userData = LocalUserService.get();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => ArtistDetailScreen(
            isOwnProfile: true,
            partialData: ArtistPartialData(
              id: userData.userId ?? 0,
              nicename: userData.nicename ?? '',
              displayName: userData.displayName ?? '',
              profileUrl: userData.profileUrl ?? '',
              avatarUrl: userData.avatarUrl ?? '',
              membershipType: userData.membershipType ?? '',
            ),
          ),
        ),
      );
    }
  }
}

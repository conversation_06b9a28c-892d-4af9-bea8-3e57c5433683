import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class CategoryPhotoIdsNotifier extends IdListNotifier {}

final categoryPhotoIdsProvider =
    NotifierProvider.autoDispose<CategoryPhotoIdsNotifier, List<int>>(
      CategoryPhotoIdsNotifier.new,
    );

final categoryPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(categoryPhotoIdsProvider);
  final store = ref.watch(photoStoreProvider);
  final items = <PhotoData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing category photos's reactivity.
/// This is the recommended way to manage category photos.
final class CategoryPhotosReactiveService {
  const CategoryPhotosReactiveService(this.ref);

  final Ref ref;

  /// Get category photos list
  List<PhotoData> getAll() {
    return ref.read(categoryPhotosProvider);
  }

  /// Get category photo IDs
  List<int> getAllIds() {
    return ref.read(categoryPhotoIdsProvider);
  }

  /// Add a new category photo (adds to both global store and category photos list)
  void addItem(PhotoData photo) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItem(photo);

    // Add to category photos list
    ref.read(categoryPhotoIdsProvider.notifier).addItem(photo.id);
  }

  /// Add multiple category photos
  void addItems(List<PhotoData> photos) {
    if (photos.isEmpty) return;

    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Add to category photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(categoryPhotoIdsProvider.notifier).addItems(photoIds);
  }

  /// Remove a category photo
  void remove(int photoId) {
    // Remove from category photos list
    ref.read(categoryPhotoIdsProvider.notifier).removeItem(photoId);
  }

  /// Remove multiple category photos
  void removeItems(List<int> photoIds) {
    if (photoIds.isEmpty) return;

    // Remove from category photos list
    ref.read(categoryPhotoIdsProvider.notifier).removeItems(photoIds);
  }

  /// Update a photo in the store (automatically reflects in category photos list)
  void updateItem(PhotoData updatedPhoto) {
    ref.read(photoStoreProvider.notifier).updateItem(updatedPhoto);
  }

  /// Update multiple photos in the store
  void updateItems(List<PhotoData> updatedPhotos) {
    ref.read(photoStoreProvider.notifier).updateItems(updatedPhotos);
  }

  /// Replace all category photos
  void replaceAll(List<PhotoData> photos) {
    // Add to global photo store
    ref.read(photoStoreProvider.notifier).addItems(photos);

    // Replace category photos list
    final photoIds = photos.map((photo) => photo.id).toList();
    ref.read(categoryPhotoIdsProvider.notifier).replaceAll(photoIds);
  }

  /// Clear all category photos
  void clear() {
    // Clear the category photos list
    ref.read(categoryPhotoIdsProvider.notifier).clear();
  }

  /// Get category photos count
  int get count => ref.read(categoryPhotoIdsProvider.notifier).length;

  /// Check if category photos list is empty
  bool get isEmpty => ref.read(categoryPhotoIdsProvider.notifier).isEmpty;

  /// Check if category photos list is not empty
  bool get isNotEmpty => ref.read(categoryPhotoIdsProvider.notifier).isNotEmpty;
}

/// Provider for the CategoryPhotosReactiveService.
final categoryPhotosReactiveServiceProvider =
    Provider.autoDispose<CategoryPhotosReactiveService>((ref) {
      return CategoryPhotosReactiveService(ref);
    });

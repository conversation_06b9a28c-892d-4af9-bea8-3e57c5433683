import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';

final class BlockedArtistIdsNotifier extends IdListNotifier {}

final blockedArtistIdsProvider =
    NotifierProvider.autoDispose<BlockedArtistIdsNotifier, List<int>>(
      BlockedArtistIdsNotifier.new,
    );

final blockedArtistsProvider = Provider.autoDispose<List<ArtistData>>((ref) {
  final ids = ref.watch(blockedArtistIdsProvider);
  final store = ref.watch(artistStoreProvider);
  final items = <ArtistData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing blocked artists's reactivity.
/// This is the recommended way to manage blocked artists.
final class BlockedArtistsReactiveService {
  const BlockedArtistsReactiveService(this.ref);

  final Ref ref;

  /// Get blocked artists list
  List<ArtistData> getAll() {
    return ref.read(blockedArtistsProvider);
  }

  /// Get blocked artist IDs
  List<int> getAllIds() {
    return ref.read(blockedArtistIdsProvider);
  }

  /// Add a new blocked artist (adds to both global store and blocked list)
  void addItem(ArtistData artist, {bool updateBlockedStatus = false}) {
    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItem(artist);

    // Add to blocked artists list
    ref.read(blockedArtistIdsProvider.notifier).addItem(artist.id);

    // Update the artist's isBlocked status in the store
    if (updateBlockedStatus) {
      final updatedArtist = artist.copyWith(isBlocked: true);
      ref.read(artistStoreProvider.notifier).updateItem(updatedArtist);
    }
  }

  /// Add multiple blocked artists
  void addItems(List<ArtistData> artists, {bool updateBlockedStatus = false}) {
    if (artists.isEmpty) return;

    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItems(artists);

    // Add to blocked artists list
    final artistIds = artists.map((artist) => artist.id).toList();
    ref.read(blockedArtistIdsProvider.notifier).addItems(artistIds);

    // Update all artists' isBlocked status in the store
    if (updateBlockedStatus) {
      final updatedArtists = artists
          .map((artist) => artist.copyWith(isBlocked: true))
          .toList();

      ref.read(artistStoreProvider.notifier).updateItems(updatedArtists);
    }
  }

  /// Remove a blocked artist
  void remove(int artistId, {bool updateBlockedStatus = false}) {
    // Remove from blocked artists list
    ref.read(blockedArtistIdsProvider.notifier).removeItem(artistId);

    // Update the artist's isBlocked status in the store if it exists
    if (updateBlockedStatus) {
      final artist = ref.read(artistStoreProvider.notifier).getItem(artistId);

      if (artist != null) {
        final updatedArtist = artist.copyWith(isBlocked: false);
        ref.read(artistStoreProvider.notifier).updateItem(updatedArtist);
      }
    }
  }

  /// Remove multiple blocked artists
  void removeItems(List<int> artistIds, {bool updateBlockedStatus = false}) {
    if (artistIds.isEmpty) return;

    // Remove from blocked artists list
    ref.read(blockedArtistIdsProvider.notifier).removeItems(artistIds);

    // Update all artists' isBlocked status in the store
    if (updateBlockedStatus) {
      final artistStore = ref.read(artistStoreProvider.notifier);
      final updatedArtists = <ArtistData>[];

      for (final artistId in artistIds) {
        final artist = artistStore.getItem(artistId);
        if (artist != null) {
          updatedArtists.add(artist.copyWith(isBlocked: false));
        }
      }

      if (updatedArtists.isNotEmpty) {
        artistStore.updateItems(updatedArtists);
      }
    }
  }

  /// Check if an artist is blocked
  bool isBlocked(int artistId) {
    return ref.read(blockedArtistIdsProvider.notifier).hasItem(artistId);
  }

  // Toggle blocked status for an artist
  void toggleBlocked(int artistId) {
    if (isBlocked(artistId)) {
      remove(artistId);
    } else {
      // Get artist from store and add to blocked if it exists
      final artist = ref.read(artistStoreProvider.notifier).getItem(artistId);
      if (artist != null) {
        addItem(artist);
      }
    }
  }

  // Update an artist in the store (automatically reflects in blocked list)
  void updateItem(ArtistData updatedArtist) {
    ref.read(artistStoreProvider.notifier).updateItem(updatedArtist);
  }

  // Update multiple artists in the store
  void updateItems(List<ArtistData> updatedArtists) {
    ref.read(artistStoreProvider.notifier).updateItems(updatedArtists);
  }

  // Replace all blocked artists
  void replaceAll(
    List<ArtistData> artists, {
    bool updateBlockedStatus = false,
  }) {
    // Add to global artist store
    ref.read(artistStoreProvider.notifier).addItems(artists);

    // Replace blocked artists list
    final artistIds = artists.map((artist) => artist.id).toList();
    ref.read(blockedArtistIdsProvider.notifier).replaceAll(artistIds);

    // Update all artists' isBlocked status in the store
    if (updateBlockedStatus) {
      final updatedArtists = artists
          .map((artist) => artist.copyWith(isBlocked: true))
          .toList();

      ref.read(artistStoreProvider.notifier).updateItems(updatedArtists);
    }
  }

  // Clear all blocked artists
  void clear({bool updateBlockedStatus = false}) {
    final currentBlockedIds = getAllIds();

    // Clear the blocked artists list
    ref.read(blockedArtistIdsProvider.notifier).clear();

    // Update all previously blocked artists' isBlocked status to false
    if (updateBlockedStatus) {
      if (currentBlockedIds.isNotEmpty) {
        final artistStore = ref.read(artistStoreProvider.notifier);
        final updatedArtists = <ArtistData>[];

        for (final artistId in currentBlockedIds) {
          final artist = artistStore.getItem(artistId);
          if (artist != null) {
            updatedArtists.add(artist.copyWith(isBlocked: false));
          }
        }

        if (updatedArtists.isNotEmpty) {
          artistStore.updateItems(updatedArtists);
        }
      }
    }
  }

  // Get blocked artists count
  int get blockedCount => ref.read(blockedArtistIdsProvider.notifier).length;

  // Check if blocked list is empty
  bool get isEmpty => ref.read(blockedArtistIdsProvider.notifier).isEmpty;

  // Check if blocked list is not empty
  bool get isNotEmpty => ref.read(blockedArtistIdsProvider.notifier).isNotEmpty;
}

/// Provider for the BlockedArtistsReactiveService.
final blockedArtistsReactiveServiceProvider =
    Provider.autoDispose<BlockedArtistsReactiveService>((ref) {
      return BlockedArtistsReactiveService(ref);
    });

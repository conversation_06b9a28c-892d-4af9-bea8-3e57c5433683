// Core packages
import 'package:flutter/material.dart';
// Extension packages

// Internal packages
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';
import 'package:portraitmode/camera/widgets/camera_detail_screen_masonry_mode.dart';

class CameraListItem extends StatelessWidget {
  const CameraListItem({
    super.key,
    required this.camera,
    this.showCount = false,
    this.borderRadius,
    this.isSquare = false,
    this.heightReducer = 0.0,
  });

  final CameraData camera;
  final bool showCount;
  final double? borderRadius;
  final bool isSquare;
  final double heightReducer;

  @override
  Widget build(BuildContext context) {
    // log('Building camera card with name is ${camera.displayName}');

    double borderRadiusValue = borderRadius == null
        ? PhotoStyleConfig.borderRadius
        : borderRadius!;

    return LayoutBuilder(
      builder: (BuildContext layoutBuildContext, BoxConstraints constraints) {
        double itemWidth = constraints.maxWidth;
        // double itemWidth = double.infinity;
        double itemHeight = isSquare
            ? itemWidth
            : itemWidth - (itemWidth / 6) - heightReducer;
        // double itemHeight = 100;

        return SizedBox(
          width: itemWidth,
          height: itemHeight,
          child: GestureDetector(
            onTap: () => _onTap(context),
            child: Stack(
              alignment: Alignment.center,
              children: [
                if (camera.latestPhotoUrl.isNotEmpty)
                  ClipRRect(
                    borderRadius: BorderRadius.all(
                      Radius.circular(borderRadiusValue),
                    ),
                    child: PmNetworkImage(
                      url: camera.latestPhotoUrl,
                      width: itemWidth,
                      height: itemHeight,
                    ),
                  ),
                Container(
                  height: itemHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(
                      Radius.circular(borderRadiusValue),
                    ),
                    // color: Colors.black.withValues(alpha: 0.5),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.6),
                        Colors.black.withValues(alpha: 0.2),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Center(
                    child: Text(
                      camera.name,
                      style: const TextStyle(
                        fontSize: 12.0,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _onTap(BuildContext context) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CameraDetailScreenMasonryMode(camera: camera),
      ),
    );
  }
}

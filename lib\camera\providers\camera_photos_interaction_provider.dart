import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/camera/dto/camera_photo_list_interaction_data.dart';

final class CameraPhotosInteractionNotifier
    extends Notifier<Map<String, CameraPhotoListInteractionData>> {
  @override
  Map<String, CameraPhotoListInteractionData> build() => {};

  Map<String, CameraPhotoListInteractionData> get data => state;

  CameraPhotoListInteractionData getDataForCamera(String cameraSlug) {
    return state[cameraSlug] ?? CameraPhotoListInteractionData();
  }

  int getLoadMoreLastId(String cameraSlug) {
    return getDataForCamera(cameraSlug).loadMoreLastId;
  }

  void setLoadMoreLastId(String cameraSlug, int lastId) {
    final currentData = getDataForCamera(cameraSlug);
    state = {
      ...state,
      cameraSlug: currentData.copyWith(loadMoreLastId: lastId),
    };
  }

  void setLastItemSeenId(String cameraSlug, int lastItemSeenId) {
    final currentData = getDataForCamera(cameraSlug);
    state = {
      ...state,
      cameraSlug: currentData.copyWith(lastItemSeenId: lastItemSeenId),
    };
  }

  void replace(String cameraSlug, CameraPhotoListInteractionData data) {
    state = {...state, cameraSlug: data};
  }

  void reset(String cameraSlug) {
    state = {...state, cameraSlug: CameraPhotoListInteractionData()};
  }

  void resetAll() {
    state = {};
  }

  void removeCamera(String cameraSlug) {
    final newState = Map<String, CameraPhotoListInteractionData>.from(state);
    newState.remove(cameraSlug);
    state = newState;
  }
}

final cameraPhotosInteractionProvider =
    NotifierProvider<
      CameraPhotosInteractionNotifier,
      Map<String, CameraPhotoListInteractionData>
    >(CameraPhotosInteractionNotifier.new);

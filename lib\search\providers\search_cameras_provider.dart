import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';
import 'package:portraitmode/camera/providers/camera_store_provider.dart';

final class SearchCameraIdsNotifier extends IdListNotifier {}

final searchCameraIdsProvider =
    NotifierProvider.autoDispose<SearchCameraIdsNotifier, List<int>>(
      SearchCameraIdsNotifier.new,
    );

final searchCamerasProvider = Provider.autoDispose<List<CameraData>>((ref) {
  final ids = ref.watch(searchCameraIdsProvider);
  final store = ref.watch(cameraStoreProvider);
  final items = <CameraData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing cameras search result reactivity.
/// This is the recommended way to manage cameras.
final class SearchCamerasReactiveService {
  const SearchCamerasReactiveService(this.ref);

  final Ref ref;

  /// Get camera list
  List<CameraData> getAll() {
    return ref.read(searchCamerasProvider);
  }

  /// Get camera IDs
  List<int> getAllIds() {
    return ref.read(searchCameraIdsProvider);
  }

  /// Add a new camera (adds to both global store and list)
  void addItem(CameraData camera) {
    // Add to global camera store
    ref.read(cameraStoreProvider.notifier).addItem(camera);

    // Add to cameras list
    ref.read(searchCameraIdsProvider.notifier).addItem(camera.id);
  }

  /// Add multiple cameras
  void addItems(List<CameraData> cameras) {
    if (cameras.isEmpty) return;

    // Add to global camera store
    ref.read(cameraStoreProvider.notifier).addItems(cameras);

    // Add to cameras list
    final cameraIds = cameras.map((camera) => camera.id).toList();
    ref.read(searchCameraIdsProvider.notifier).addItems(cameraIds);
  }

  /// Remove a camera
  void remove(int cameraId) {
    // Remove from cameras list
    ref.read(searchCameraIdsProvider.notifier).removeItem(cameraId);
  }

  /// Remove multiple cameras
  void removeItems(List<int> cameraIds) {
    if (cameraIds.isEmpty) return;

    // Remove from cameras list
    ref.read(searchCameraIdsProvider.notifier).removeItems(cameraIds);
  }

  /// Update a camera in the store (automatically reflects in list)
  void updateItem(CameraData updatedCamera) {
    ref.read(cameraStoreProvider.notifier).updateItem(updatedCamera);
  }

  /// Update multiple cameras in the store
  void updateItems(List<CameraData> updatedCameras) {
    ref.read(cameraStoreProvider.notifier).updateItems(updatedCameras);
  }

  /// Replace all cameras
  void replaceAll(List<CameraData> cameras) {
    // Add to global camera store
    ref.read(cameraStoreProvider.notifier).addItems(cameras);

    // Replace cameras list
    final cameraIds = cameras.map((camera) => camera.id).toList();
    ref.read(searchCameraIdsProvider.notifier).replaceAll(cameraIds);
  }

  /// Clear all cameras
  void clear() {
    // Clear the cameras list
    ref.read(searchCameraIdsProvider.notifier).clear();
  }

  /// Get cameras count
  int get count => ref.read(searchCameraIdsProvider.notifier).length;

  /// Check if list is empty
  bool get isEmpty => ref.read(searchCameraIdsProvider.notifier).isEmpty;

  /// Check if list is not empty
  bool get isNotEmpty => ref.read(searchCameraIdsProvider.notifier).isNotEmpty;
}

/// Provider for the SearchCamerasReactiveService.
final searchCamerasReactiveServiceProvider =
    Provider.autoDispose<SearchCamerasReactiveService>((ref) {
      return SearchCamerasReactiveService(ref);
    });
